"""
Test Script for Import Fix

This script validates that the select_element_interactively function
is correctly imported from core.interactive_selector with the new parameters.
"""

import sys
import os
import inspect

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import_fix():
    """Test that the import fix works correctly."""
    print("🧪 Testing import fix for select_element_interactively...")
    
    try:
        # Test the import from stage4
        from stages.stage4 import select_element_interactively
        
        # Check the function signature
        sig = inspect.signature(select_element_interactively)
        params = list(sig.parameters.keys())
        
        print(f"📋 Function parameters: {params}")
        
        # Check for the new parameters
        required_params = ['replay_steps', 'step_table', 'current_step_index', 'test_data', 'progress_callback']
        
        missing_params = []
        for param in required_params:
            if param not in params:
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ Missing parameters: {missing_params}")
            return False
        
        print("✅ All required parameters found in function signature")
        
        # Test that we can call the function with the new parameters (dry run)
        try:
            # Get default values to test the signature
            defaults = {}
            for param_name, param in sig.parameters.items():
                if param.default != inspect.Parameter.empty:
                    defaults[param_name] = param.default
            
            print(f"📋 Default values: {defaults}")
            
            # Verify the new parameters have appropriate defaults
            expected_defaults = {
                'replay_steps': False,
                'step_table': None,
                'current_step_index': 0,
                'test_data': None,
                'progress_callback': None
            }
            
            for param, expected_default in expected_defaults.items():
                if param in defaults:
                    actual_default = defaults[param]
                    if actual_default != expected_default:
                        print(f"❌ Parameter '{param}' has unexpected default: {actual_default} (expected: {expected_default})")
                        return False
                else:
                    print(f"❌ Parameter '{param}' missing default value")
                    return False
            
            print("✅ All parameter defaults are correct")
            
        except Exception as e:
            print(f"❌ Error testing function signature: {e}")
            return False
        
        print("✅ Import fix successful - function has correct signature")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_direct_import():
    """Test direct import from core.interactive_selector."""
    print("🧪 Testing direct import from core.interactive_selector...")
    
    try:
        from core.interactive_selector import select_element_interactively
        
        # Check the function signature
        sig = inspect.signature(select_element_interactively)
        params = list(sig.parameters.keys())
        
        print(f"📋 Direct import parameters: {params}")
        
        # Check for the new parameters
        required_params = ['replay_steps', 'step_table', 'current_step_index', 'test_data', 'progress_callback']
        
        missing_params = []
        for param in required_params:
            if param not in params:
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ Missing parameters in direct import: {missing_params}")
            return False
        
        print("✅ Direct import has all required parameters")
        return True
        
    except Exception as e:
        print(f"❌ Direct import test failed: {e}")
        return False

def test_old_import():
    """Test that the old import from element_detection still works but has old signature."""
    print("🧪 Testing old import from core.element_detection...")
    
    try:
        from core.element_detection import select_element_interactively as old_function
        
        # Check the function signature
        sig = inspect.signature(old_function)
        params = list(sig.parameters.keys())
        
        print(f"📋 Old import parameters: {params}")
        
        # The old function should NOT have the new parameters
        new_params = ['replay_steps', 'step_table', 'current_step_index', 'test_data', 'progress_callback']
        
        has_new_params = []
        for param in new_params:
            if param in params:
                has_new_params.append(param)
        
        if has_new_params:
            print(f"❌ Old function unexpectedly has new parameters: {has_new_params}")
            return False
        
        print("✅ Old import correctly has old signature (no new parameters)")
        return True
        
    except Exception as e:
        print(f"❌ Old import test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Testing Import Fix for select_element_interactively")
    print("=" * 60)
    
    tests = [
        ("Import Fix (stage4)", test_import_fix),
        ("Direct Import", test_direct_import),
        ("Old Import Check", test_old_import)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Import fix is working correctly.")
        print("💡 stage4.py now imports the enhanced function with stateful element selection support.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
