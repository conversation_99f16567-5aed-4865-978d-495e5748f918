"""
Test Script for Nested Expander Fix

This script validates that the stateful element selector no longer creates
nested expanders that cause Streamlit API exceptions.

The fix removes the st.expander() wrapper from _display_stateful_element_selection_controls()
and replaces it with inline content using st.markdown() sections.
"""

import sys
import os
import re

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_no_nested_expanders():
    """Test that the stateful element selection controls don't use nested expanders."""
    print("🧪 Testing for nested expander removal...")
    
    try:
        # Read the stage4.py file
        stage4_path = os.path.join(os.path.dirname(__file__), 'stages', 'stage4.py')
        
        with open(stage4_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the _display_stateful_element_selection_controls function
        function_pattern = r'def _display_stateful_element_selection_controls\(.*?\):(.*?)(?=def |\Z)'
        match = re.search(function_pattern, content, re.DOTALL)
        
        if not match:
            print("❌ Could not find _display_stateful_element_selection_controls function")
            return False
        
        function_content = match.group(1)
        
        # Check that there's no st.expander() call in the function
        if 'st.expander(' in function_content:
            print("❌ Found st.expander() call in _display_stateful_element_selection_controls")
            print("This will cause nested expander errors when called from within another expander")
            return False
        
        # Check that the function uses st.markdown() for headers instead
        if '### 🔄 Stateful Element Selection Settings' not in function_content:
            print("❌ Missing expected markdown header for stateful element selection")
            return False
        
        # Check for horizontal rules (separators) instead of expander
        if 'st.markdown("---")' not in function_content:
            print("❌ Missing expected markdown separators")
            return False
        
        print("✅ No nested expanders found in _display_stateful_element_selection_controls")
        print("✅ Function uses inline content with markdown headers and separators")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_function_structure():
    """Test that the function maintains expected structure and functionality."""
    print("🧪 Testing function structure and content...")
    
    try:
        # Read the stage4.py file
        stage4_path = os.path.join(os.path.dirname(__file__), 'stages', 'stage4.py')
        
        with open(stage4_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the function
        function_pattern = r'def _display_stateful_element_selection_controls\(.*?\):(.*?)(?=def |\Z)'
        match = re.search(function_pattern, content, re.DOTALL)
        
        if not match:
            print("❌ Could not find function")
            return False
        
        function_content = match.group(1)
        
        # Check for essential components (using simple string searches)
        required_elements = [
            'replay_context = state.get_step_replay_context()',
            'st.checkbox(',
            'previous steps before element selection',
            'state.set_step_replay_preference(',
            'Steps to Replay:',
            'st.button("🔄 Reset"'
        ]

        missing_elements = []
        for element in required_elements:
            if element not in function_content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing required elements: {missing_elements}")
            return False
        
        print("✅ Function maintains all required functionality")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_ui_element_detection_context():
    """Test that the function is called from the correct context."""
    print("🧪 Testing UI element detection context...")
    
    try:
        # Read the stage4.py file
        stage4_path = os.path.join(os.path.dirname(__file__), 'stages', 'stage4.py')
        
        with open(stage4_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find where the function is called
        call_pattern = r'_display_stateful_element_selection_controls\(state\)'
        calls = re.findall(call_pattern, content)
        
        if not calls:
            print("❌ Function is not being called")
            return False
        
        # Find the calling context
        calling_function_pattern = r'def _handle_interactive_element_selection\(.*?\):(.*?)(?=def |\Z)'
        match = re.search(calling_function_pattern, content, re.DOTALL)
        
        if not match:
            print("❌ Could not find calling function")
            return False
        
        calling_content = match.group(1)
        
        if '_display_stateful_element_selection_controls(state)' not in calling_content:
            print("❌ Function not called from expected location")
            return False
        
        print("✅ Function is called from correct context")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Testing Nested Expander Fix")
    print("=" * 50)
    
    tests = [
        ("No Nested Expanders", test_no_nested_expanders),
        ("Function Structure", test_function_structure),
        ("UI Element Detection Context", test_ui_element_detection_context)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Nested expander issue is fixed.")
        print("💡 The stateful element selector now uses inline content instead of nested expanders.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
