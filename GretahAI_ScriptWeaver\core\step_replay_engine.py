"""
Step Replay Engine for GretahAI ScriptWeaver

This module provides functionality to replay test steps in a browser session
to reach the correct state for interactive element selection in multi-step scenarios.

Key Features:
- Executes test steps sequentially to build up browser state
- Supports various action types (navigate, click, type, select, etc.)
- Handles dynamic content loading and page transitions
- Provides error handling and fallback mechanisms
- Maintains browser session state for stateful element selection
- Integrates with existing step data structure from StateManager
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    ElementNotInteractableException,
    StaleElementReferenceException,
    WebDriverException
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.step_replay_engine")

class StepReplayEngine:
    """
    Engine for replaying test steps to build browser state for element selection.
    
    This class executes test steps sequentially in a browser session to reach
    the correct page state before opening the interactive element selector.
    """
    
    def __init__(self, driver: webdriver.Chrome):
        """
        Initialize the step replay engine.
        
        Args:
            driver: Selenium WebDriver instance to use for step execution
        """
        self.driver = driver
        self.executed_steps = []
        self.step_context = {}
        self.last_error = None
        
    def replay_steps_to_current(self, step_table: List[Dict[str, Any]], 
                               current_step_index: int,
                               website_url: str,
                               test_data: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Replay all steps up to (but not including) the current step index.
        
        Args:
            step_table: List of test steps in automation-ready format
            current_step_index: Index of the current step (0-based)
            website_url: Base website URL for navigation
            test_data: Test data dictionary for parameter substitution
            
        Returns:
            Tuple of (success: bool, message: str)
        """
        logger.info(f"=== Starting step replay to reach step {current_step_index + 1} ===")
        
        if not step_table or current_step_index <= 0:
            logger.info("No previous steps to replay")
            return True, "No previous steps to replay"
            
        try:
            # Reset state
            self.executed_steps = []
            self.step_context = {}
            self.last_error = None
            
            # Execute steps sequentially up to current step
            for i in range(current_step_index):
                step = step_table[i]
                step_no = step.get('step_no', i + 1)
                
                logger.info(f"Replaying step {step_no}: {step.get('action', 'unknown')}")
                
                success, error_msg = self._execute_single_step(step, website_url, test_data)
                
                if not success:
                    error_message = f"Step {step_no} failed during replay: {error_msg}"
                    logger.error(error_message)
                    self.last_error = error_message
                    return False, error_message
                    
                self.executed_steps.append(step)
                
                # Brief pause between steps to allow page transitions
                time.sleep(0.5)
                
            success_message = f"Successfully replayed {len(self.executed_steps)} steps"
            logger.info(f"=== {success_message} ===")
            return True, success_message
            
        except Exception as e:
            error_message = f"Unexpected error during step replay: {str(e)}"
            logger.error(error_message, exc_info=True)
            self.last_error = error_message
            return False, error_message
    
    def _execute_single_step(self, step: Dict[str, Any], 
                           website_url: str,
                           test_data: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Execute a single test step in the browser.
        
        Args:
            step: Step dictionary with action, locator, etc.
            website_url: Base website URL
            test_data: Test data for parameter substitution
            
        Returns:
            Tuple of (success: bool, error_message: str)
        """
        try:
            action = step.get('action', '').lower()
            locator_strategy = step.get('locator_strategy', '')
            locator = step.get('locator', '')
            test_data_param = step.get('test_data_param', '')
            timeout = int(step.get('timeout', 10))
            
            # Substitute test data parameters
            if test_data_param and test_data:
                # Handle {{parameter}} format
                param_key = test_data_param.strip('{}')
                if param_key in test_data:
                    test_data_param = test_data[param_key]
            
            logger.debug(f"Executing action: {action}, locator: {locator_strategy}={locator}")
            
            if action == 'navigate':
                return self._execute_navigate(locator or website_url, timeout)
            elif action == 'click':
                return self._execute_click(locator_strategy, locator, timeout)
            elif action in ['type', 'enter_text', 'input']:
                return self._execute_type(locator_strategy, locator, test_data_param, timeout)
            elif action == 'select':
                return self._execute_select(locator_strategy, locator, test_data_param, timeout)
            elif action == 'wait':
                return self._execute_wait(locator_strategy, locator, timeout)
            elif action in ['verify', 'assert']:
                # For replay, we'll skip verification steps to focus on state building
                logger.debug(f"Skipping verification step during replay: {action}")
                return True, "Verification step skipped during replay"
            else:
                logger.warning(f"Unknown action type: {action}")
                return True, f"Unknown action '{action}' skipped during replay"
                
        except Exception as e:
            error_msg = f"Error executing step: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False, error_msg
    
    def _execute_navigate(self, url: str, timeout: int) -> Tuple[bool, str]:
        """Execute navigation action."""
        try:
            logger.debug(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            return True, f"Successfully navigated to {url}"
            
        except TimeoutException:
            return False, f"Timeout waiting for page to load: {url}"
        except Exception as e:
            return False, f"Navigation failed: {str(e)}"
    
    def _execute_click(self, locator_strategy: str, locator: str, timeout: int) -> Tuple[bool, str]:
        """Execute click action."""
        try:
            element = self._find_element(locator_strategy, locator, timeout)
            if not element:
                return False, f"Element not found: {locator_strategy}={locator}"
            
            # Scroll element into view
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.2)
            
            # Try to click the element
            element.click()
            
            return True, f"Successfully clicked element: {locator}"
            
        except ElementNotInteractableException:
            return False, f"Element not interactable: {locator}"
        except Exception as e:
            return False, f"Click failed: {str(e)}"
    
    def _execute_type(self, locator_strategy: str, locator: str, text: str, timeout: int) -> Tuple[bool, str]:
        """Execute type/input action."""
        try:
            element = self._find_element(locator_strategy, locator, timeout)
            if not element:
                return False, f"Element not found: {locator_strategy}={locator}"
            
            # Clear existing text and type new text
            element.clear()
            if text:
                element.send_keys(text)
            
            return True, f"Successfully typed text into element: {locator}"
            
        except ElementNotInteractableException:
            return False, f"Element not interactable: {locator}"
        except Exception as e:
            return False, f"Type action failed: {str(e)}"
    
    def _execute_select(self, locator_strategy: str, locator: str, value: str, timeout: int) -> Tuple[bool, str]:
        """Execute select dropdown action."""
        try:
            element = self._find_element(locator_strategy, locator, timeout)
            if not element:
                return False, f"Element not found: {locator_strategy}={locator}"
            
            select = Select(element)
            
            # Try different selection methods
            try:
                select.select_by_visible_text(value)
            except NoSuchElementException:
                try:
                    select.select_by_value(value)
                except NoSuchElementException:
                    select.select_by_index(int(value))
            
            return True, f"Successfully selected option: {value}"
            
        except Exception as e:
            return False, f"Select action failed: {str(e)}"
    
    def _execute_wait(self, locator_strategy: str, locator: str, timeout: int) -> Tuple[bool, str]:
        """Execute wait action."""
        try:
            if locator_strategy and locator:
                # Wait for specific element
                element = self._find_element(locator_strategy, locator, timeout)
                if element:
                    return True, f"Element appeared: {locator}"
                else:
                    return False, f"Element did not appear within timeout: {locator}"
            else:
                # Simple time-based wait
                time.sleep(min(timeout, 5))  # Cap at 5 seconds for replay
                return True, f"Waited {timeout} seconds"
                
        except Exception as e:
            return False, f"Wait action failed: {str(e)}"
    
    def _find_element(self, locator_strategy: str, locator: str, timeout: int):
        """Find element using the specified locator strategy."""
        try:
            wait = WebDriverWait(self.driver, timeout)
            
            if locator_strategy.lower() == 'id':
                return wait.until(EC.presence_of_element_located((By.ID, locator)))
            elif locator_strategy.lower() == 'name':
                return wait.until(EC.presence_of_element_located((By.NAME, locator)))
            elif locator_strategy.lower() in ['css', 'css_selector']:
                return wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, locator)))
            elif locator_strategy.lower() == 'xpath':
                return wait.until(EC.presence_of_element_located((By.XPATH, locator)))
            elif locator_strategy.lower() == 'class':
                return wait.until(EC.presence_of_element_located((By.CLASS_NAME, locator)))
            elif locator_strategy.lower() == 'tag':
                return wait.until(EC.presence_of_element_located((By.TAG_NAME, locator)))
            elif locator_strategy.lower() == 'link_text':
                return wait.until(EC.presence_of_element_located((By.LINK_TEXT, locator)))
            elif locator_strategy.lower() == 'partial_link_text':
                return wait.until(EC.presence_of_element_located((By.PARTIAL_LINK_TEXT, locator)))
            else:
                logger.warning(f"Unknown locator strategy: {locator_strategy}")
                return None
                
        except TimeoutException:
            logger.debug(f"Element not found within timeout: {locator_strategy}={locator}")
            return None
        except Exception as e:
            logger.error(f"Error finding element: {str(e)}")
            return None
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get summary of executed steps."""
        return {
            'executed_steps_count': len(self.executed_steps),
            'executed_steps': [step.get('step_no', i+1) for i, step in enumerate(self.executed_steps)],
            'last_error': self.last_error,
            'current_url': self.driver.current_url if self.driver else None
        }
