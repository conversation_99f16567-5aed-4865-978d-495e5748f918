"""
Test Script for Stateful Element Selector Enhancement

This script validates the stateful element selector functionality by testing:
1. Step replay engine functionality
2. StateManager integration
3. Interactive selector enhancements
4. Error handling and fallback mechanisms

Run this script to verify the implementation works correctly.
"""

import sys
import os
import logging
from typing import Dict, List, Any

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_stateful_element_selector")

def test_step_replay_engine():
    """Test the StepReplayEngine functionality."""
    print("🧪 Testing StepReplayEngine...")
    
    try:
        from core.step_replay_engine import StepReplayEngine
        from core.elements import setup_webdriver
        
        # Create a test driver (headless for testing)
        driver = setup_webdriver(headless=True)
        if not driver:
            print("❌ Failed to create WebDriver for testing")
            return False
        
        # Create replay engine
        replay_engine = StepReplayEngine(driver)
        
        # Test data
        test_steps = [
            {
                'step_no': 1,
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': 'https://example.com',
                'test_data_param': '',
                'timeout': 10
            }
        ]
        
        # Test replay functionality
        success, message = replay_engine.replay_steps_to_current(
            test_steps, 1, 'https://example.com', {}
        )
        
        if success:
            print("✅ StepReplayEngine basic functionality works")
        else:
            print(f"❌ StepReplayEngine failed: {message}")
            
        # Test execution summary
        summary = replay_engine.get_execution_summary()
        print(f"📊 Execution summary: {summary}")
        
        # Cleanup
        driver.quit()
        return success
        
    except Exception as e:
        print(f"❌ StepReplayEngine test failed: {e}")
        return False

def test_state_manager_integration():
    """Test StateManager integration for stateful element selection."""
    print("🧪 Testing StateManager integration...")
    
    try:
        from state_manager import StateManager
        
        # Create test state manager
        state = StateManager()
        
        # Test stateful element selection fields
        assert hasattr(state, 'enable_stateful_element_selection'), "Missing enable_stateful_element_selection field"
        assert hasattr(state, 'step_replay_enabled'), "Missing step_replay_enabled field"
        assert hasattr(state, 'step_replay_progress'), "Missing step_replay_progress field"
        assert hasattr(state, 'step_replay_last_error'), "Missing step_replay_last_error field"
        assert hasattr(state, 'browser_session_state'), "Missing browser_session_state field"
        
        # Test helper methods
        state.set_step_replay_preference(True)
        assert state.step_replay_enabled == True, "set_step_replay_preference not working"
        
        state.update_step_replay_progress("Test progress")
        assert state.step_replay_progress == "Test progress", "update_step_replay_progress not working"
        
        state.set_step_replay_error("Test error")
        assert state.step_replay_last_error == "Test error", "set_step_replay_error not working"
        
        state.clear_step_replay_state()
        assert state.step_replay_progress is None, "clear_step_replay_state not working"
        assert state.step_replay_last_error is None, "clear_step_replay_state not working"
        
        # Test get_step_replay_context
        context = state.get_step_replay_context()
        assert isinstance(context, dict), "get_step_replay_context should return dict"
        assert 'enabled' in context, "Missing 'enabled' in context"
        
        print("✅ StateManager integration works correctly")
        return True
        
    except Exception as e:
        print(f"❌ StateManager integration test failed: {e}")
        return False

def test_interactive_selector_enhancements():
    """Test interactive selector enhancements."""
    print("🧪 Testing interactive selector enhancements...")

    try:
        # Import the module and check source code directly
        import core.interactive_selector as selector_module
        import inspect

        # Get the source code to verify function definitions
        source = inspect.getsource(selector_module)

        # Check if the enhanced function signatures are in the source
        required_patterns = [
            "replay_steps=False",
            "step_table=None",
            "current_step_index=0",
            "test_data=None",
            "progress_callback=None"
        ]

        missing_patterns = []
        for pattern in required_patterns:
            if pattern not in source:
                missing_patterns.append(pattern)

        if missing_patterns:
            print(f"❌ Missing patterns in source code: {missing_patterns}")
            return False

        # Check if StepReplayEngine import is present
        if "from core.step_replay_engine import StepReplayEngine" not in source:
            print("❌ Missing StepReplayEngine import")
            return False

        # Check if step replay logic is present
        if "replay_engine.replay_steps_to_current" not in source:
            print("❌ Missing step replay logic")
            return False

        print("✅ Interactive selector enhancements are correctly implemented")
        return True

    except Exception as e:
        print(f"❌ Interactive selector enhancement test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stage4_integration():
    """Test Stage 4 integration."""
    print("🧪 Testing Stage 4 integration...")
    
    try:
        from stages.stage4 import _display_stateful_element_selection_controls, _handle_interactive_element_selection
        import inspect
        
        # Check if functions exist
        assert callable(_display_stateful_element_selection_controls), "Missing _display_stateful_element_selection_controls function"
        assert callable(_handle_interactive_element_selection), "Missing _handle_interactive_element_selection function"
        
        # Check function signatures
        display_sig = inspect.signature(_display_stateful_element_selection_controls)
        handle_sig = inspect.signature(_handle_interactive_element_selection)
        
        # Verify parameter counts
        assert len(display_sig.parameters) == 1, "Wrong parameter count for _display_stateful_element_selection_controls"
        assert len(handle_sig.parameters) == 3, "Wrong parameter count for _handle_interactive_element_selection"
        
        print("✅ Stage 4 integration is correctly implemented")
        return True
        
    except Exception as e:
        print(f"❌ Stage 4 integration test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Starting Stateful Element Selector Tests")
    print("=" * 60)
    
    tests = [
        ("StepReplayEngine", test_step_replay_engine),
        ("StateManager Integration", test_state_manager_integration),
        ("Interactive Selector Enhancements", test_interactive_selector_enhancements),
        ("Stage 4 Integration", test_stage4_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Stateful Element Selector implementation is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
